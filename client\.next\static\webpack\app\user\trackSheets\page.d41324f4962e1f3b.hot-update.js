"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/ClientSelectPage.tsx":
/*!***************************************************!*\
  !*** ./app/user/trackSheets/ClientSelectPage.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_component_FancySelect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/_component/FancySelect */ \"(app-pages-browser)/./app/_component/FancySelect.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _ExportTrackSheet__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ExportTrackSheet */ \"(app-pages-browser)/./app/user/trackSheets/ExportTrackSheet.tsx\");\n/* harmony import */ var _ViewTrackSheet__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ViewTrackSheet */ \"(app-pages-browser)/./app/user/trackSheets/ViewTrackSheet.tsx\");\n/* harmony import */ var _TrackSheetContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./TrackSheetContext */ \"(app-pages-browser)/./app/user/trackSheets/TrackSheetContext.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst ClientSelectPage = (param)=>{\n    let { permissions, client, carrierDataUpdate, clientDataUpdate, userData } = param;\n    _s();\n    const [customFieldsMap, setCustomFieldsMap] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [selectedClients, setSelectedClients] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trackSheetData, setTrackSheetData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        data: [],\n        datalength: 0\n    });\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    const pageSize = parseInt(searchParams.get(\"pageSize\")) || 50;\n    const totalPages = Math.ceil(((trackSheetData === null || trackSheetData === void 0 ? void 0 : trackSheetData.datalength) || 0) / pageSize);\n    const params = new URLSearchParams(searchParams);\n    const clientOptions = (client === null || client === void 0 ? void 0 : client.map((c)=>{\n        var _c_id;\n        return {\n            value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n            label: c.client_name\n        };\n    })) || [];\n    const mapCustomFields = (data)=>data.map((row)=>({\n                ...row,\n                customFields: (row.TrackSheetCustomFieldMapping || []).reduce((acc, mapping)=>{\n                    acc[mapping.customFieldId] = mapping.value;\n                    return acc;\n                }, {})\n            }));\n    const handleClientChange = (newSelectedClients)=>{\n        setSelectedClients(newSelectedClients);\n    };\n    // const  {customFieldsReloadTrigger} = useContext()\n    const { customFieldsReloadTrigger, deleteData, warningFilter } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_TrackSheetContext__WEBPACK_IMPORTED_MODULE_8__.TrackSheetContext);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            if (selectedClients.length > 0) {\n                try {\n                    var _selectedClients_, _selectedClients_1;\n                    const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getAllData)(\"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_3__.clientCustomFields_routes.GET_CLIENT_CUSTOM_FIELDS, \"/\").concat((_selectedClients_ = selectedClients[0]) === null || _selectedClients_ === void 0 ? void 0 : _selectedClients_.value));\n                    const fieldsMap = {};\n                    (response.custom_fields || []).forEach((field)=>{\n                        fieldsMap[field.id] = {\n                            name: field.name,\n                            type: field.type\n                        };\n                    });\n                    setCustomFieldsMap(fieldsMap);\n                    if (!params.get(\"page\")) params.set(\"page\", \"1\");\n                    if (!params.get(\"pageSize\")) params.set(\"pageSize\", \"50\");\n                    let url = \"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_3__.trackSheets_routes.GETALL_TRACK_SHEETS, \"/\").concat((_selectedClients_1 = selectedClients[0]) === null || _selectedClients_1 === void 0 ? void 0 : _selectedClients_1.value, \"?\").concat(params.toString());\n                    if (warningFilter !== null) {\n                        url += \"&systemGeneratedWarnings=\".concat(warningFilter);\n                    }\n                    const trackSheetResponse = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getAllData)(url);\n                    setTrackSheetData({\n                        ...trackSheetResponse,\n                        data: mapCustomFields(trackSheetResponse.data || [])\n                    });\n                } catch (error) {\n                    setCustomFieldsMap({});\n                    setTrackSheetData({\n                        data: [],\n                        datalength: 0\n                    });\n                }\n            } else {\n                setCustomFieldsMap({});\n                setTrackSheetData({\n                    data: [],\n                    datalength: 0\n                });\n            }\n        };\n        fetchData();\n    }, [\n        selectedClients,\n        searchParams,\n        customFieldsReloadTrigger,\n        deleteData,\n        warningFilter\n    ]);\n    const [columnVisibility, setColumnVisibility] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const selectedClientObj = client === null || client === void 0 ? void 0 : client.find((c)=>{\n        var _c_id, _selectedClients_;\n        return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === ((_selectedClients_ = selectedClients[0]) === null || _selectedClients_ === void 0 ? void 0 : _selectedClients_.value);\n    });\n    const showLegrandColumns = (selectedClientObj === null || selectedClientObj === void 0 ? void 0 : selectedClientObj.client_name) === \"LEGRAND\";\n    const showOrcaColumns = (selectedClientObj === null || selectedClientObj === void 0 ? void 0 : selectedClientObj.associate.name) === \"ORCA\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FancySelect__WEBPACK_IMPORTED_MODULE_1__.FancySelect, {\n                            frameworks: clientOptions,\n                            selected: selectedClients,\n                            setSelected: handleClientChange,\n                            label: \"Select Client\",\n                            placeholder: \"Search clients...\",\n                            className: \"max-w-xs\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ClientSelectPage.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ClientSelectPage.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, undefined),\n                    selectedClients.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExportTrackSheet__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        filteredTrackSheetData: trackSheetData === null || trackSheetData === void 0 ? void 0 : trackSheetData.data,\n                        customFieldsMap: customFieldsMap,\n                        columnVisibility: columnVisibility,\n                        showOrcaColumns: showOrcaColumns,\n                        warningFilter: warningFilter\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ClientSelectPage.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ClientSelectPage.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full animate-in fade-in duration-500 rounded-2xl shadow-sm dark:bg-gray-800 p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ViewTrackSheet__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    permissions: permissions,\n                    totalPages: totalPages,\n                    customFieldsMap: customFieldsMap,\n                    selectedClients: selectedClients,\n                    trackSheetData: trackSheetData,\n                    pageSize: pageSize,\n                    carrierDataUpdate: carrierDataUpdate,\n                    clientDataUpdate: clientDataUpdate,\n                    setColumnVisibility: setColumnVisibility,\n                    userData: userData,\n                    showOrcaColumns: showOrcaColumns,\n                    showLegrandColumns: showLegrandColumns\n                }, void 0, false, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ClientSelectPage.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ClientSelectPage.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ClientSelectPage.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClientSelectPage, \"F8I2RH5/RXrb9SEjbA8xeGlO9Og=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams\n    ];\n});\n_c = ClientSelectPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ClientSelectPage);\nvar _c;\n$RefreshReg$(_c, \"ClientSelectPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/ClientSelectPage.tsx\n"));

/***/ })

});
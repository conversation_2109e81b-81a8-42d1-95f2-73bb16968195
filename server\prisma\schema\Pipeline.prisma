enum Worktype {
    trackSheets
    invoiceFiles
}

model Pipeline {
    id       String   @id @default(uuid())
    name     String?  @db.VarChar()
    description String? @db.VarChar()
    workType       Worktype  @map("work_type")
    isActive Boolean? @default(true) @map("is_active")

    stages PipelineStage[]

    // tickets   Ticket[]
    corporationId Int?         @map("corporation_id")
    corporation   Corporation? @relation(fields: [corporationId], references: [corporation_id], onDelete: Cascade)
    Ticket        Ticket[]

    createdAt DateTime  @default(now()) @map("created_at")
    createdBy String?   @map("created_by")
    updatedAt DateTime? @updatedAt @map("updated_at")
    updatedBy String?   @map("updated_by")
    deletedAt DateTime? @map("deleted_at")
    deletedBy String?   @map("deleted_by")
    @@map("pipelines")
}
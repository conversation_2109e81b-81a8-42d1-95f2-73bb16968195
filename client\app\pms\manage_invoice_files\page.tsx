import React from "react";
import { getAllData, getCookie } from "@/lib/helpers";
import { carrier_routes, employee_routes } from "@/lib/routePath";
import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import { PermissionWrapper } from "@/lib/permissionWrapper";
import InvoiceFilesBoard from "./InvoiceFilesBoard";

const InvoiceFilesPage = async () => {
  const userData = await getAllData(
    `${employee_routes.GETCURRENT_USER}?TracksheetUser=true`
  );  

  const userPermissions =
    userData?.role?.role_permission.map(
      (item: any) => item.permission.action
    ) || [];
  const corporationCookie = await getCookie("corporationtoken");
  const allCarrierResponse = await getAllData(
    `${carrier_routes.GETALL_CARRIER}?notIncludeWorkReport=true`
  );
  const allCarrier = allCarrierResponse?.data ?? [];
  const users = await getAllData(employee_routes.GETALL_USERS);
  const permissions = corporationCookie ? ["allow_all"] : userPermissions;

  return (
    <>
      <div className="w-full p-2 pl-4">
        <div className="h-9 flex items-center">
          <AdminNavBar
            link={"/pms/manage_invoice_files"}
            name={"Manage Invoice Files"}
          />
        </div>
        <div className="w-full">
          <PermissionWrapper
            permissions={permissions}
            requiredPermissions={["view-invoice-files"]}
          >
            <InvoiceFilesBoard
              userData={userData}
              carrier={allCarrier}
              users={users}
            />
          </PermissionWrapper>
        </div>
      </div>
    </>
  );
};

export default InvoiceFilesPage;

"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TrackSheetWarningService = void 0;
const warningMessages_1 = require("../constants/warningMessages");
const prismaClient_1 = __importDefault(require("../utils/prismaClient"));
/**
 * Service for generating system warnings based on freight terms and address types
 */
class TrackSheetWarningService {
    /**
     * Generate warnings for a tracksheet based on freight term and address types
     */
    static generateWarnings(data) {
        const warnings = [];
        try {
            // Skip warning generation for legacy records
            if (!data.freightTerm || data.freightTerm === 'LEGACY') {
                return warnings;
            }
            // Validate freight term
            if (!Object.values(warningMessages_1.FREIGHT_TERMS).includes(data.freightTerm)) {
                warnings.push({
                    severity: warningMessages_1.WARNING_SEVERITY.CRITICAL,
                    message: warningMessages_1.WARNING_MESSAGES.CRITICAL.INVALID_FREIGHT_TERM
                });
                return warnings;
            }
            // Validate address types
            const addressTypes = [
                data.shipperAddressType,
                data.consigneeAddressType,
                data.billToAddressType
            ];
            for (const addressType of addressTypes) {
                if (addressType &&
                    addressType !== 'LEGACY' &&
                    !Object.values(warningMessages_1.ADDRESS_TYPES).includes(addressType)) {
                    warnings.push({
                        severity: warningMessages_1.WARNING_SEVERITY.CRITICAL,
                        message: warningMessages_1.WARNING_MESSAGES.CRITICAL.INVALID_ADDRESS_TYPE
                    });
                    return warnings;
                }
            }
            // Generate freight-term specific warnings
            warnings.push(...this.generateFreightTermWarnings(data));
            // Generate informational warnings
            warnings.push(...this.generateInformationalWarnings(data));
        }
        catch (error) {
            console.error('Error generating warnings:', error);
            warnings.push({
                severity: warningMessages_1.WARNING_SEVERITY.CRITICAL,
                message: warningMessages_1.WARNING_MESSAGES.CRITICAL.SYSTEM_ERROR
            });
        }
        return warnings;
    }
    /**
     * Generate warnings specific to freight terms and responsible parties
     */
    static generateFreightTermWarnings(data) {
        const warnings = [];
        if (!data.freightTerm || data.freightTerm === 'LEGACY') {
            return warnings;
        }
        const responsibleParty = warningMessages_1.RESPONSIBLE_PARTY_MAP[data.freightTerm];
        switch (data.freightTerm) {
            case warningMessages_1.FREIGHT_TERMS.PREPAID:
                if (data.shipperAddressType === warningMessages_1.ADDRESS_TYPES.CV) {
                    warnings.push({
                        severity: warningMessages_1.WARNING_SEVERITY.HIGH,
                        message: warningMessages_1.WARNING_MESSAGES.HIGH.PREPAID_SHIPPER_CV
                    });
                    warnings.push({
                        severity: warningMessages_1.WARNING_SEVERITY.HIGH,
                        message: warningMessages_1.WARNING_MESSAGES.HIGH.PAYMENT_IMPACT
                    });
                }
                break;
            case warningMessages_1.FREIGHT_TERMS.COLLECT:
                if (data.consigneeAddressType === warningMessages_1.ADDRESS_TYPES.CV) {
                    warnings.push({
                        severity: warningMessages_1.WARNING_SEVERITY.HIGH,
                        message: warningMessages_1.WARNING_MESSAGES.HIGH.COLLECT_CONSIGNEE_CV
                    });
                    warnings.push({
                        severity: warningMessages_1.WARNING_SEVERITY.HIGH,
                        message: warningMessages_1.WARNING_MESSAGES.HIGH.PAYMENT_IMPACT
                    });
                }
                break;
            case warningMessages_1.FREIGHT_TERMS.THIRD_PARTY:
                if (data.billToAddressType === warningMessages_1.ADDRESS_TYPES.CV) {
                    warnings.push({
                        severity: warningMessages_1.WARNING_SEVERITY.HIGH,
                        message: warningMessages_1.WARNING_MESSAGES.HIGH.THIRD_PARTY_BILL_TO_CV
                    });
                    warnings.push({
                        severity: warningMessages_1.WARNING_SEVERITY.HIGH,
                        message: warningMessages_1.WARNING_MESSAGES.HIGH.PAYMENT_IMPACT
                    });
                }
                break;
        }
        return warnings;
    }
    /**
     * Generate informational warnings
     */
    static generateInformationalWarnings(data) {
        const warnings = [];
        // Check for non-responsible party CV selections
        const cvAddressTypes = [];
        if (data.shipperAddressType === warningMessages_1.ADDRESS_TYPES.CV)
            cvAddressTypes.push('shipper');
        if (data.consigneeAddressType === warningMessages_1.ADDRESS_TYPES.CV)
            cvAddressTypes.push('consignee');
        if (data.billToAddressType === warningMessages_1.ADDRESS_TYPES.CV)
            cvAddressTypes.push('billTo');
        if (cvAddressTypes.length > 0) {
            const responsibleParty = data.freightTerm ?
                warningMessages_1.RESPONSIBLE_PARTY_MAP[data.freightTerm] : null;
            const nonResponsibleCvs = cvAddressTypes.filter(party => party !== responsibleParty);
            // if (nonResponsibleCvs.length > 0) {
            //   warnings.push({
            //     severity: WARNING_SEVERITY.MEDIUM,
            //     message: WARNING_MESSAGES.MEDIUM.NON_RESPONSIBLE_CV
            //   });
            // }
            // Check for partial CV selection
            // if (cvAddressTypes.length > 0 && cvAddressTypes.length < 3) {
            //   warnings.push({
            //     severity: WARNING_SEVERITY.MEDIUM,
            //     message: WARNING_MESSAGES.MEDIUM.PARTIAL_CV_SELECTION
            //   });
            // }
        }
        // Add validation completion message for successful validations
        // if (warnings.length === 0) {
        //   warnings.push({
        //     severity: WARNING_SEVERITY.MEDIUM,
        //     message: WARNING_MESSAGES.MEDIUM.FREIGHT_TERM_VALIDATION
        //   });
        // }
        return warnings;
    }
    /**
     * Prepare warning data for database insertion
     */
    static prepareWarningsForDb(trackSheetId, warnings, createdBy) {
        return warnings.map(warning => ({
            trackSheetId,
            severity: warning.severity,
            message: warning.message,
            createdBy: createdBy || 'SYSTEM'
        }));
    }
    /**
     * Save warnings to database
     */
    static async saveWarnings(warningsData) {
        try {
            if (warningsData.length === 0)
                return [];
            const result = await prismaClient_1.default.systemGeneratedWarning.createMany({
                data: warningsData,
            });
            return result;
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * Generate and save warnings in one operation
     */
    static async generateAndSaveWarnings(trackSheetId, warningData, createdBy) {
        try {
            const warnings = this.generateWarnings(warningData);
            if (warnings.length > 0) {
                const warningsForDb = this.prepareWarningsForDb(trackSheetId, warnings, createdBy);
                await this.saveWarnings(warningsForDb);
            }
            return warnings;
        }
        catch (warningError) {
            console.error('Error generating/saving warnings for tracksheet:', trackSheetId, warningError);
            // Don't fail the entire operation if warning generation fails
            return [];
        }
    }
}
exports.TrackSheetWarningService = TrackSheetWarningService;
//# sourceMappingURL=trackSheetWarningService.js.map
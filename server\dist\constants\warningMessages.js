"use strict";
/**
 * Warning Messages Constants for TrackSheet System Generated Warnings
 *
 * This file contains predefined warning messages categorized by severity level
 * for the freight term-based company+division mapping system.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.RESPONSIBLE_PARTY_MAP = exports.ADDRESS_TYPES = exports.FREIGHT_TERMS = exports.WARNING_SEVERITY = exports.WARNING_MESSAGES = void 0;
exports.WARNING_MESSAGES = {
    HIGH: {
        PREPAID_SHIPPER_CV: "Manual address entry required - CV selected for freight-responsible party (Shipper) with PREPAID freight term",
        COLLECT_CONSIGNEE_CV: "Manual address entry required - CV selected for freight-responsible party (Consignee) with COLLECT freight term",
        THIRD_PARTY_BILL_TO_CV: "Manual address entry required - CV selected for freight-responsible party (Bill To) with THIRD_PARTY freight term",
        PAYMENT_IMPACT: "Invoice payment may be impacted due to manual address entry for freight-responsible party"
    },
    // MEDIUM: {
    //   NON_RESPONSIBLE_CV: "Informational: Custom address entry detected for non-freight-responsible party",
    //   PARTIAL_CV_SELECTION: "Informational: Mix of DC and CV address types detected",
    //   FREIGHT_TERM_VALIDATION: "Informational: Freight term and address type validation completed"
    // },
    CRITICAL: {
        INVALID_FREIGHT_TERM: "Critical: Invalid freight term provided",
        INVALID_ADDRESS_TYPE: "Critical: Invalid address type provided",
        SYSTEM_ERROR: "Critical: System error occurred during warning generation"
    }
};
/**
 * Warning severity levels
 */
exports.WARNING_SEVERITY = {
    HIGH: "HIGH",
    // MEDIUM: "MEDIUM", 
    CRITICAL: "CRITICAL"
};
/**
 * Freight terms mapping
 */
exports.FREIGHT_TERMS = {
    PREPAID: "PREPAID",
    COLLECT: "COLLECT",
    THIRD_PARTY: "THIRD_PARTY"
};
/**
 * Address types mapping
 */
exports.ADDRESS_TYPES = {
    DC: "DC",
    CV: "CV"
};
/**
 * Responsible party mapping based on freight terms
 */
exports.RESPONSIBLE_PARTY_MAP = {
    [exports.FREIGHT_TERMS.PREPAID]: "shipper",
    [exports.FREIGHT_TERMS.COLLECT]: "consignee",
    [exports.FREIGHT_TERMS.THIRD_PARTY]: "billTo"
};
//# sourceMappingURL=warningMessages.js.map
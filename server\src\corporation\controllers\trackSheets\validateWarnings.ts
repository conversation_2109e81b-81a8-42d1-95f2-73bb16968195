import { handleError } from "../../../utils/helpers";
import { TrackSheetWarningService } from "../../../services/trackSheetWarningService";

/**
 * Validate warnings for tracksheet data in real-time
 * Used by frontend for dynamic validation during form input
 */
export const validateWarnings = async (req, res) => {
  try {
    const { freightTerm, shipperAddressType, consigneeAddressType, billToAddressType } = req.body;

    // Validate required fields
    if (!freightTerm || !shipperAddressType || !consigneeAddressType || !billToAddressType) {
      return res.status(400).json({
        success: false,
        message: "Missing required fields: freightTerm, shipperAddressType, consigneeAddressType, billToAddressType",
        error: "VALIDATION_ERROR"
      });
    }

    // Generate warnings using the existing service
    const warnings = TrackSheetWarningService.generateWarnings({
      freightTerm,
      shipperAddressType,
      consigneeAddressType,
      billToAddressType
    });

    // Categorize warnings by severity
    const categorizedWarnings = {
      HIGH: warnings.filter(w => w.severity === 'HIGH'),
      // MEDIUM: warnings.filter(w => w.severity === 'MEDIUM'),
      CRITICAL: warnings.filter(w => w.severity === 'CRITICAL')
    };

    // Generate summary
    const summary = {
      totalWarnings: warnings.length,
      highWarnings: categorizedWarnings.HIGH.length,
      // mediumWarnings: categorizedWarnings.MEDIUM.length,
      criticalWarnings: categorizedWarnings.CRITICAL.length
    };

    return res.status(200).json({
      success: true,
      warnings: categorizedWarnings,
      summary
    });

  } catch (error) {
    console.error("Error validating warnings:", error);
    return handleError(res, error);
  }
};
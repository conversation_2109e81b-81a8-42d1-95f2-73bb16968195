import { 
  WARNING_MESSAGES, 
  WARNING_SEVERITY, 
  FREIGHT_TERMS, 
  ADDRESS_TYPES, 
  RESPONSIBLE_PARTY_MAP 
} from '../constants/warningMessages';
import prisma from '../utils/prismaClient';

export interface TrackSheetWarningData {
  freightTerm?: string;
  shipperAddressType?: string;
  consigneeAddressType?: string;
  billToAddressType?: string;
}

export interface GeneratedWarning {
  severity: 'HIGH' | 'MEDIUM' | 'CRITICAL';
  message: string;
}

/**
 * Service for generating system warnings based on freight terms and address types
 */
export class TrackSheetWarningService {
  
  /**
   * Generate warnings for a tracksheet based on freight term and address types
   */
  static generateWarnings(data: TrackSheetWarningData): GeneratedWarning[] {
    const warnings: GeneratedWarning[] = [];

    try {
      // Skip warning generation for legacy records
      if (!data.freightTerm || data.freightTerm === 'LEGACY') {
        return warnings;
      }

      // Validate freight term
      if (!Object.values(FREIGHT_TERMS).includes(data.freightTerm as any)) {
        warnings.push({
          severity: WARNING_SEVERITY.CRITICAL,
          message: WARNING_MESSAGES.CRITICAL.INVALID_FREIGHT_TERM
        });
        return warnings;
      }

      // Validate address types
      const addressTypes = [
        data.shipperAddressType,
        data.consigneeAddressType, 
        data.billToAddressType
      ];

      for (const addressType of addressTypes) {
        if (addressType && 
            addressType !== 'LEGACY' && 
            !Object.values(ADDRESS_TYPES).includes(addressType as any)) {
          warnings.push({
            severity: WARNING_SEVERITY.CRITICAL,
            message: WARNING_MESSAGES.CRITICAL.INVALID_ADDRESS_TYPE
          });
          return warnings;
        }
      }

      // Generate freight-term specific warnings
      warnings.push(...this.generateFreightTermWarnings(data));

      // Generate informational warnings
      warnings.push(...this.generateInformationalWarnings(data));

    } catch (error) {
      console.error('Error generating warnings:', error);
      warnings.push({
        severity: WARNING_SEVERITY.CRITICAL,
        message: WARNING_MESSAGES.CRITICAL.SYSTEM_ERROR
      });
    }

    return warnings;
  }

  /**
   * Generate warnings specific to freight terms and responsible parties
   */
  private static generateFreightTermWarnings(data: TrackSheetWarningData): GeneratedWarning[] {
    const warnings: GeneratedWarning[] = [];
    
    if (!data.freightTerm || data.freightTerm === 'LEGACY') {
      return warnings;
    }

    const responsibleParty = RESPONSIBLE_PARTY_MAP[data.freightTerm as keyof typeof RESPONSIBLE_PARTY_MAP];
    
    switch (data.freightTerm) {
      case FREIGHT_TERMS.PREPAID:
        if (data.shipperAddressType === ADDRESS_TYPES.CV) {
          warnings.push({
            severity: WARNING_SEVERITY.HIGH,
            message: WARNING_MESSAGES.HIGH.PREPAID_SHIPPER_CV
          });
          warnings.push({
            severity: WARNING_SEVERITY.HIGH,
            message: WARNING_MESSAGES.HIGH.PAYMENT_IMPACT
          });
        }
        break;

      case FREIGHT_TERMS.COLLECT:
        if (data.consigneeAddressType === ADDRESS_TYPES.CV) {
          warnings.push({
            severity: WARNING_SEVERITY.HIGH,
            message: WARNING_MESSAGES.HIGH.COLLECT_CONSIGNEE_CV
          });
          warnings.push({
            severity: WARNING_SEVERITY.HIGH,
            message: WARNING_MESSAGES.HIGH.PAYMENT_IMPACT
          });
        }
        break;

      case FREIGHT_TERMS.THIRD_PARTY:
        if (data.billToAddressType === ADDRESS_TYPES.CV) {
          warnings.push({
            severity: WARNING_SEVERITY.HIGH,
            message: WARNING_MESSAGES.HIGH.THIRD_PARTY_BILL_TO_CV
          });
          warnings.push({
            severity: WARNING_SEVERITY.HIGH,
            message: WARNING_MESSAGES.HIGH.PAYMENT_IMPACT
          });
        }
        break;
    }

    return warnings;
  }

  /**
   * Generate informational warnings
   */
  private static generateInformationalWarnings(data: TrackSheetWarningData): GeneratedWarning[] {
    const warnings: GeneratedWarning[] = [];

    // Check for non-responsible party CV selections
    const cvAddressTypes = [];
    if (data.shipperAddressType === ADDRESS_TYPES.CV) cvAddressTypes.push('shipper');
    if (data.consigneeAddressType === ADDRESS_TYPES.CV) cvAddressTypes.push('consignee');
    if (data.billToAddressType === ADDRESS_TYPES.CV) cvAddressTypes.push('billTo');

    if (cvAddressTypes.length > 0) {
      const responsibleParty = data.freightTerm ? 
        RESPONSIBLE_PARTY_MAP[data.freightTerm as keyof typeof RESPONSIBLE_PARTY_MAP] : null;
      
      const nonResponsibleCvs = cvAddressTypes.filter(party => party !== responsibleParty);
      
      // if (nonResponsibleCvs.length > 0) {
      //   warnings.push({
      //     severity: WARNING_SEVERITY.MEDIUM,
      //     message: WARNING_MESSAGES.MEDIUM.NON_RESPONSIBLE_CV
      //   });
      // }

      // Check for partial CV selection
      // if (cvAddressTypes.length > 0 && cvAddressTypes.length < 3) {
      //   warnings.push({
      //     severity: WARNING_SEVERITY.MEDIUM,
      //     message: WARNING_MESSAGES.MEDIUM.PARTIAL_CV_SELECTION
      //   });
      // }
    }

    // Add validation completion message for successful validations
    // if (warnings.length === 0) {
    //   warnings.push({
    //     severity: WARNING_SEVERITY.MEDIUM,
    //     message: WARNING_MESSAGES.MEDIUM.FREIGHT_TERM_VALIDATION
    //   });
    // }

    return warnings;
  }

  /**
   * Prepare warning data for database insertion
   */
  static prepareWarningsForDb(
    trackSheetId: number, 
    warnings: GeneratedWarning[], 
    createdBy?: string
  ) {
    return warnings.map(warning => ({
      trackSheetId,
      severity: warning.severity,
      message: warning.message,
      createdBy: createdBy || 'SYSTEM'
    }));
  }

  /**
   * Save warnings to database
   */
  static async saveWarnings(warningsData: any[]) {
    try {
      if (warningsData.length === 0) return [];
      
      const result = await prisma.systemGeneratedWarning.createMany({
        data: warningsData,
      });
      return result;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Generate and save warnings in one operation
   */
  static async generateAndSaveWarnings(
    trackSheetId: number,
    warningData: TrackSheetWarningData,
    createdBy?: string
  ): Promise<GeneratedWarning[]> {
    try {
      const warnings = this.generateWarnings(warningData);

      if (warnings.length > 0) {
        const warningsForDb = this.prepareWarningsForDb(
          trackSheetId,
          warnings,
          createdBy
        );

        await this.saveWarnings(warningsForDb);
      }

      return warnings;
    } catch (warningError) {
      console.error('Error generating/saving warnings for tracksheet:', trackSheetId, warningError);
      // Don't fail the entire operation if warning generation fails
      return [];
    }
  }
}

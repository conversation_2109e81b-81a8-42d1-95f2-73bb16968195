// Test file to demonstrate the workItemId helper function
// This shows how the helper handles both integer and string workItemIds
// Helper function to safely convert workItemId for SQL queries
const getWorkItemIdForQuery = (workItemId) => {
    // Check if the workItemId is a valid number
    const numericId = parseInt(workItemId, 10);
    if (!isNaN(numericId) && numericId.toString() === workItemId) {
        // If it's a valid integer, return it as is for SQL
        return workItemId;
    }
    // If it's not a valid integer, wrap it in quotes for SQL string comparison
    return `'${workItemId.replace(/'/g, "''")}'`; // Escape single quotes for SQL safety
};
// Test cases
console.log("Testing workItemId helper function:");
console.log("=====================================");
// Test with integer strings (old tracksheet IDs)
console.log("Integer string '123':", getWorkItemIdForQuery("123"));
console.log("Integer string '456':", getWorkItemIdForQuery("456"));
// Test with UUID strings (new module IDs)
console.log("UUID 'abc-123-def':", getWorkItemIdForQuery("abc-123-def"));
console.log("UUID 'uuid-456-xyz':", getWorkItemIdForQuery("uuid-456-xyz"));
// Test with strings containing quotes (edge case)
console.log("String with quote 'test'quote':", getWorkItemIdForQuery("test'quote"));
// Test with mixed cases
console.log("Mixed '123abc':", getWorkItemIdForQuery("123abc"));
console.log("Mixed 'abc123':", getWorkItemIdForQuery("abc123"));
console.log("\nSQL Query Examples:");
console.log("==================");
// Example SQL queries
const testWorkItemIds = ["123", "abc-123-def", "test'quote"];
testWorkItemIds.forEach(id => {
    const sqlValue = getWorkItemIdForQuery(id);
    console.log(`SELECT * FROM track_sheets WHERE id = ${sqlValue};`);
});
console.log("\nThis demonstrates how the helper function:");
console.log("- Keeps numeric strings as-is for SQL (no quotes needed)");
console.log("- Wraps non-numeric strings in quotes for SQL");
console.log("- Properly escapes single quotes in strings");
//# sourceMappingURL=test-workitemid-helper.js.map
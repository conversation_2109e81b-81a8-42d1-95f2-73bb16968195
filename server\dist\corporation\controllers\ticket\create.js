"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLowestOrderTicketStage = exports.createTicket = void 0;
const createTicket = async (req, res) => {
    const tickets = req.body;
    if (!Array.isArray(tickets) || tickets.length === 0) {
        return res.status(400).json({
            message: "Request body must be an array of tickets.",
        });
    }
    const results = [];
    for (const ticketData of tickets) {
        try {
            const { tracksheetid, pipeline_id, stages, owner, priority, createdBy, title, description } = ticketData;
            if (!tracksheetid ||
                !pipeline_id ||
                !Array.isArray(stages) ||
                stages.length === 0) {
                results.push({
                    status: "failed",
                    data: ticketData,
                    error: "Each ticket must have tracksheetid, pipeline_id, and stages.",
                });
                continue;
            }
            // Check if a ticket with this workItemId already exists
            const existingTicket = await prisma.ticket.findUnique({
                where: { workItemId: String(tracksheetid) },
            });
            if (existingTicket) {
                results.push({
                    status: "failed",
                    data: ticketData,
                    error: `A ticket for this workItemId ${tracksheetid} already exists`,
                });
                continue;
            }
            // Find the pipeline stage with the lowest order to determine the initial current stage
            const lowestOrderPipelineStage = await prisma.pipelineStage.findFirst({
                where: {
                    id: {
                        in: stages.map(stage => stage.stageid),
                    },
                },
                orderBy: {
                    order: 'asc',
                },
            });
            const ticket = await prisma.ticket.create({
                data: {
                    title: title || `Invoice ${tracksheetid}`,
                    description: description || "",
                    workItemId: String(tracksheetid),
                    pipelineId: pipeline_id,
                    owner,
                    priority,
                    createdBy: createdBy,
                    updatedBy: createdBy,
                },
            });
            const createdStages = [];
            // let currentStageId = null;
            for (const stage of stages) {
                const createdStage = await prisma.ticketStage.create({
                    data: {
                        ticketId: ticket.id,
                        pipelineStageId: stage.stageid,
                        assignedTo: stage.assignedto,
                        dueAt: stage.due ? new Date(stage.due) : undefined,
                        createdBy: createdBy,
                    },
                });
                createdStages.push(createdStage);
                // Set the current stage ID to the ticket stage that corresponds to the lowest order pipeline stage
                //   if (stage.stageid === lowestOrderPipelineStage?.id) {
                //     currentStageId = createdStage.id;
                //   }
            }
            // Update the ticket with the current stage ID
            const updatedTicket = await prisma.ticket.update({
                where: {
                    id: ticket.id,
                },
                data: {
                    currentStageId: lowestOrderPipelineStage?.id,
                },
            });
            results.push({
                status: "success",
                ticket: updatedTicket,
                stages: createdStages,
            });
        }
        catch (error) {
            results.push({
                status: "failed",
                data: ticketData,
                error: error.message,
            });
        }
    }
    const allSucceeded = results.every((r) => r.status === "success");
    // Collect all error messages from failed tickets
    const errorMessages = results
        .filter((r) => r.status === "failed" && r.error)
        .map((r) => r.error)
        .join("; ");
    return res.status(allSucceeded ? 201 : 207).json({
        message: allSucceeded
            ? "All tickets created successfully."
            : errorMessages || "Some tickets could not be created.",
        results,
    });
};
exports.createTicket = createTicket;
// get the lowest order ticketStage for a ticket
const getLowestOrderTicketStage = async (ticketId) => {
    const lowestOrderStage = await prisma.ticketStage.findFirst({
        where: {
            ticketId: ticketId,
        },
        include: {
            pipelineStage: true,
        },
        orderBy: {
            pipelineStage: {
                order: 'asc',
            },
        },
    });
    return lowestOrderStage;
};
exports.getLowestOrderTicketStage = getLowestOrderTicketStage;
//# sourceMappingURL=create.js.map
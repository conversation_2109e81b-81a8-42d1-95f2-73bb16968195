import prisma from "../../../utils/prismaClient";
import { handleError } from "../../../utils/helpers";
import { Worktype } from "@prisma/client";
import { Response } from "express";

const workTypeToTableMapping: Record<Worktype, string> = {
  trackSheets: "track_sheets",
  invoiceFiles: "invoice_files",
};

export const viewTicket = async (req: any, res: any) => {
  try {
    const data = await prisma.ticket.findMany({
      where: { deletedAt: null },
      include: {
        pipeline: { include: { stages: true } },
        stages: true,
        comments: {
          where: { deletedAt: null },
          orderBy: { createdAt: "desc" },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Collect all unique assignedTo values from ticket.stages only
    const allAssignedTo = new Set<string>();
    data.forEach(ticket => {
      if (ticket.stages) {
        ticket.stages.forEach(stage => {
          if (stage.assignedTo) allAssignedTo.add(stage.assignedTo);
        });
      }
    });
    // Fetch user details for all assignedTo values (by username or id)
    const assignedToArr = Array.from(allAssignedTo);
    const users = assignedToArr.length > 0 ? await prisma.user.findMany({
      where: {
        OR: [
          { username: { in: assignedToArr } },
          { id: { in: assignedToArr.filter(v => !isNaN(Number(v))).map(v => Number(v)) } },
        ],
      },
      select: { id: true, username: true, firstName: true, lastName: true, email: true },
    }) : [];
    // Helper to get user by assignedTo value
    const getUser = (assignedTo: string) => users.find(u => u.username === assignedTo || String(u.id) === String(assignedTo));

    const enhancedData = await Promise.all(
      data.map(async (ticket) => {
        const table = workTypeToTableMapping[ticket.pipeline.workType as Worktype];
        // Fetch actual tag objects for the tag IDs
        const tags = ticket.tags.length > 0 ? await prisma.tag.findMany({
          where: {
            id: {
              in: ticket.tags,
            },
            deletedAt: null,
          },
        }) : [];
        // Enrich ticket stages with assignedUser
        const stages = ticket.stages ? ticket.stages.map(stage => ({
          ...stage,
          assignedUser: stage.assignedTo ? getUser(stage.assignedTo) : null,
        })) : ticket.stages;
        // Do not enrich pipeline.stages (no assignedTo field)
        const pipeline = ticket.pipeline ? {
          ...ticket.pipeline,
          stages: ticket.pipeline.stages,
        } : ticket.pipeline;
        if (table && ticket.workItemId) {
   const fetchedRecord = await prisma.$queryRawUnsafe(`SELECT * FROM ${table} WHERE id = $1`, ticket.workItemId);

          return {
            ...ticket,
            TicketTracksheet: fetchedRecord[0] || null,
            tags: tags,
            pipeline,
            stages,
          };
        }
        return {
          ...ticket,
          tags: tags,
          pipeline,
          stages,
        };
      })
    );
    return res.status(200).json({
      data: await Promise.all(enhancedData),
    });
  } catch (error) {
    return handleError(res, error);
  }
};

interface CurrentUserTicketRequest {
  user_id?: string;
  body: {
    userId?: string;
  };
}

interface TicketWithDetails {
  id: string;
  tags: any[];
  pipeline: any;
  stages: any[];
  TicketTracksheet?: any;
  [key: string]: any;
}

interface CurrentUserTicketResponse {
  data: Array<TicketWithDetails>;
}

export const getCurrentUserTickets = async (
  req: CurrentUserTicketRequest,
  res: Response<CurrentUserTicketResponse | { error: string }>
) => {
  try {
    const currentUserId = req.user_id || req.body.userId;
    
    if (!currentUserId) {
      return res.status(401).json({ error: "Unauthorized" });
    }
    // Fetch all tickets with their stages
    const data = await prisma.ticket.findMany({
      where: {
        deletedAt: null,
      },
      include: {
        pipeline: { include: { stages: true } },
        stages: true,
        comments: {
          where: { deletedAt: null },
          orderBy: { createdAt: "desc" },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });    

    // Filter tickets to only those where the current user is assigned to the current stage
    const filteredData = data.filter(ticket => {
      if (!ticket.currentStageId || !ticket.stages || ticket.stages.length === 0) return false;
      // Find the TicketStage with pipelineStageId === currentStageId
      const currentStage = ticket.stages.find(stage => stage.pipelineStageId === ticket.currentStageId);
      return currentStage && currentStage.assignedTo === String(currentUserId);
    });

    // Collect all unique assignedTo values from ticket.stages only (for filtered tickets)
    const allAssignedTo = new Set<string>();
    filteredData.forEach(ticket => {
      if (ticket.stages) {
        ticket.stages.forEach(stage => {
          if (stage.assignedTo) allAssignedTo.add(stage.assignedTo);
        });
      }
    });
    // Fetch user details for all assignedTo values (by username or id)
    const assignedToArr = Array.from(allAssignedTo);
    const users = assignedToArr.length > 0 ? await prisma.user.findMany({
      where: {
        OR: [
          { username: { in: assignedToArr } },
          { id: { in: assignedToArr.filter(v => !isNaN(Number(v))).map(v => Number(v)) } },
        ],
      },
      select: { id: true, username: true, firstName: true, lastName: true, email: true },
    }) : [];
    // Helper to get user by assignedTo value
    const getUser = (assignedTo: string) => users.find(u => u.username === assignedTo || String(u.id) === String(assignedTo));

    const enhancedData = await Promise.all(
      filteredData.map(async (ticket) => {
        const table = workTypeToTableMapping[ticket.pipeline.workType as Worktype];
        // Fetch actual tag objects for the tag IDs
        const tags = ticket.tags.length > 0 ? await prisma.tag.findMany({
          where: {
            id: {
              in: ticket.tags,
            },
            deletedAt: null,
          },
        }) : [];
        // Enrich ticket stages with assignedUser
        const stages = ticket.stages ? ticket.stages.map(stage => ({
          ...stage,
          assignedUser: stage.assignedTo ? getUser(stage.assignedTo) : null,
        })) : ticket.stages;
        // Do not enrich pipeline.stages (no assignedTo field)
        const pipeline = ticket.pipeline ? {
          ...ticket.pipeline,
          stages: ticket.pipeline.stages,
        } : ticket.pipeline;
        if (table && ticket.workItemId) {
const fetchedRecord = await prisma.$queryRawUnsafe(`SELECT * FROM ${table} WHERE id = $1`, ticket.workItemId);

          return {
            ...ticket,
            TicketTracksheet: fetchedRecord[0] || null,
            tags: tags,
            pipeline,
            stages,
          };
        }
        return {
          ...ticket,
          tags: tags,
          pipeline,
          stages,
        };
      })
    );
    return res.status(200).json({
      data: await Promise.all(enhancedData),
    });
  } catch (error) {
    return handleError(res, error);
  }
};
export const viewTicketById = async (req: any, res: any) => {
  try {
    const { id } = req.params;
    if (!id) {
      return res.status(400).json({ error: "Ticket id is required" });
    }
    const data = await prisma.ticket.findUnique({
      where: { id: id },
      include: {
        pipeline: { include: { stages: true } },
        stages: true,
        comments: {
          where: { deletedAt: null },
          orderBy: { createdAt: "desc" },
        },
      },
    });
    if (!data) {
      return res.status(404).json({ error: "Ticket not found" });
    }
    // Collect all unique assignedTo values from ticket.stages only
    const allAssignedTo = new Set<string>();
    if (data.stages) {
      data.stages.forEach(stage => {
        if (stage.assignedTo) allAssignedTo.add(stage.assignedTo);
      });
    }
    const assignedToArr = Array.from(allAssignedTo);
    const users = assignedToArr.length > 0 ? await prisma.user.findMany({
      where: {
        OR: [
          { username: { in: assignedToArr } },
          { id: { in: assignedToArr.filter(v => !isNaN(Number(v))).map(v => Number(v)) } },
        ],
      },
      select: { id: true, username: true, firstName: true, lastName: true, email: true },
    }) : [];
    const getUser = (assignedTo: string) => users.find(u => u.username === assignedTo || String(u.id) === String(assignedTo));
    // Fetch actual tag objects for the tag IDs
    const tags = data.tags.length > 0 ? await prisma.tag.findMany({
      where: {
        id: {
          in: data.tags,
        },
        deletedAt: null,
      },
    }) : [];
    // Enrich ticket stages with assignedUser
    const stages = data.stages ? data.stages.map(stage => ({
      ...stage,
      assignedUser: stage.assignedTo ? getUser(stage.assignedTo) : null,
    })) : data.stages;
    // Do not enrich pipeline.stages (no assignedTo field)
    const pipeline = data.pipeline ? {
      ...data.pipeline,
      stages: data.pipeline.stages,
    } : data.pipeline;
    const table = workTypeToTableMapping[data.pipeline.workType as Worktype];
    let enhancedData: any = data;
    if (table && data.workItemId) {
      const query = `select * from ${table} where id = ${data.workItemId}`;
      const fetchedRecord = await prisma.$queryRawUnsafe(query);
      enhancedData = {
        ...data,
        workItem: fetchedRecord[0] || null,
        tags: tags,
        pipeline,
        stages,
      };
    } else {
      enhancedData = {
        ...data,
        tags: tags,
        pipeline,
        stages,
      };
    }
    return res.status(200).json({
      data: enhancedData,
    });
  } catch (error) {
    return handleError(res, error);
  }
};

// Get ticket stage change logs
export const getTicketStageChangeLogs = async (req: any, res: any) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({ error: "Ticket id is required" });
    }

    const logs = await prisma.ticketStageChangeLog.findMany({
      where: {
        ticketId: id,
        deletedAt: null,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Enhance logs with pipeline stage names
    const enhancedLogs = await Promise.all(
      logs.map(async (log) => {
        let fromStageName = "Unknown Stage";
        let toStageName = "Unknown Stage";

        if (log.fromStage) {
          const fromPipelineStage = await prisma.pipelineStage.findUnique({
            where: { id: log.fromStage },
            select: { name: true },
          });
          fromStageName = fromPipelineStage?.name || "Unknown Stage";
        }

        if (log.toStage) {
          const toPipelineStage = await prisma.pipelineStage.findUnique({
            where: { id: log.toStage },
            select: { name: true },
          });
          toStageName = toPipelineStage?.name || "Unknown Stage";
        }

        return {
          id: log.id,
          ticketId: log.ticketId,
          fromStage: log.fromStage,
          toStage: log.toStage,
          fromStageName,
          toStageName,
          createdBy: log.createdBy,
          createdAt: log.createdAt,
          updatedAt: log.updatedAt,
          updatedBy: log.updatedBy,
        };
      })
    );

    return res.status(200).json({
      success: true,
      data: enhancedLogs,
    });
  } catch (error) {
    return handleError(res, error);
  }
};

model Ticket {
    id          String  @id @default(uuid())
    title       String? @db.Var<PERSON>har()
    description String? @db.Text()
    owner       String? @db.VarChar()
    priority    String? @db.VarChar()
    workItemId  String     @unique @map("work_item_id")

    pipelineId String?   @map("pipeline_id")
    pipeline   Pipeline? @relation(fields: [pipelineId], references: [id], onDelete: Cascade)

    stages   TicketStage[]
    comments Comment[]
    tags     String[]      @db.VarChar()

    currentStageId String? @map("current_stage_id")

    createdAt DateTime  @default(now()) @map("created_at")
    createdBy String?   @map("created_by")
    updatedAt DateTime? @updatedAt @map("updated_at")
    updatedBy String?   @map("updated_by")
    deletedAt DateTime? @map("deleted_at")
    deletedBy String?   @map("deleted_by")

    @@map("tickets")
}

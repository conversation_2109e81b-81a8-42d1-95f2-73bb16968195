{"version": 3, "file": "trackSheetWarningService.js", "sourceRoot": "", "sources": ["../../src/services/trackSheetWarningService.ts"], "names": [], "mappings": ";;;;;;AAAA,kEAMsC;AACtC,yEAA2C;AAc3C;;GAEG;AACH,MAAa,wBAAwB;IAEnC;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,IAA2B;QACjD,MAAM,QAAQ,GAAuB,EAAE,CAAC;QAExC,IAAI,CAAC;YACH,6CAA6C;YAC7C,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE,CAAC;gBACvD,OAAO,QAAQ,CAAC;YAClB,CAAC;YAED,wBAAwB;YACxB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,+BAAa,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAkB,CAAC,EAAE,CAAC;gBACpE,QAAQ,CAAC,IAAI,CAAC;oBACZ,QAAQ,EAAE,kCAAgB,CAAC,QAAQ;oBACnC,OAAO,EAAE,kCAAgB,CAAC,QAAQ,CAAC,oBAAoB;iBACxD,CAAC,CAAC;gBACH,OAAO,QAAQ,CAAC;YAClB,CAAC;YAED,yBAAyB;YACzB,MAAM,YAAY,GAAG;gBACnB,IAAI,CAAC,kBAAkB;gBACvB,IAAI,CAAC,oBAAoB;gBACzB,IAAI,CAAC,iBAAiB;aACvB,CAAC;YAEF,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;gBACvC,IAAI,WAAW;oBACX,WAAW,KAAK,QAAQ;oBACxB,CAAC,MAAM,CAAC,MAAM,CAAC,+BAAa,CAAC,CAAC,QAAQ,CAAC,WAAkB,CAAC,EAAE,CAAC;oBAC/D,QAAQ,CAAC,IAAI,CAAC;wBACZ,QAAQ,EAAE,kCAAgB,CAAC,QAAQ;wBACnC,OAAO,EAAE,kCAAgB,CAAC,QAAQ,CAAC,oBAAoB;qBACxD,CAAC,CAAC;oBACH,OAAO,QAAQ,CAAC;gBAClB,CAAC;YACH,CAAC;YAED,0CAA0C;YAC1C,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC,CAAC;YAEzD,kCAAkC;YAClC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,CAAC,CAAC;QAE7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,QAAQ,CAAC,IAAI,CAAC;gBACZ,QAAQ,EAAE,kCAAgB,CAAC,QAAQ;gBACnC,OAAO,EAAE,kCAAgB,CAAC,QAAQ,CAAC,YAAY;aAChD,CAAC,CAAC;QACL,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,2BAA2B,CAAC,IAA2B;QACpE,MAAM,QAAQ,GAAuB,EAAE,CAAC;QAExC,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE,CAAC;YACvD,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,MAAM,gBAAgB,GAAG,uCAAqB,CAAC,IAAI,CAAC,WAAiD,CAAC,CAAC;QAEvG,QAAQ,IAAI,CAAC,WAAW,EAAE,CAAC;YACzB,KAAK,+BAAa,CAAC,OAAO;gBACxB,IAAI,IAAI,CAAC,kBAAkB,KAAK,+BAAa,CAAC,EAAE,EAAE,CAAC;oBACjD,QAAQ,CAAC,IAAI,CAAC;wBACZ,QAAQ,EAAE,kCAAgB,CAAC,IAAI;wBAC/B,OAAO,EAAE,kCAAgB,CAAC,IAAI,CAAC,kBAAkB;qBAClD,CAAC,CAAC;oBACH,QAAQ,CAAC,IAAI,CAAC;wBACZ,QAAQ,EAAE,kCAAgB,CAAC,IAAI;wBAC/B,OAAO,EAAE,kCAAgB,CAAC,IAAI,CAAC,cAAc;qBAC9C,CAAC,CAAC;gBACL,CAAC;gBACD,MAAM;YAER,KAAK,+BAAa,CAAC,OAAO;gBACxB,IAAI,IAAI,CAAC,oBAAoB,KAAK,+BAAa,CAAC,EAAE,EAAE,CAAC;oBACnD,QAAQ,CAAC,IAAI,CAAC;wBACZ,QAAQ,EAAE,kCAAgB,CAAC,IAAI;wBAC/B,OAAO,EAAE,kCAAgB,CAAC,IAAI,CAAC,oBAAoB;qBACpD,CAAC,CAAC;oBACH,QAAQ,CAAC,IAAI,CAAC;wBACZ,QAAQ,EAAE,kCAAgB,CAAC,IAAI;wBAC/B,OAAO,EAAE,kCAAgB,CAAC,IAAI,CAAC,cAAc;qBAC9C,CAAC,CAAC;gBACL,CAAC;gBACD,MAAM;YAER,KAAK,+BAAa,CAAC,WAAW;gBAC5B,IAAI,IAAI,CAAC,iBAAiB,KAAK,+BAAa,CAAC,EAAE,EAAE,CAAC;oBAChD,QAAQ,CAAC,IAAI,CAAC;wBACZ,QAAQ,EAAE,kCAAgB,CAAC,IAAI;wBAC/B,OAAO,EAAE,kCAAgB,CAAC,IAAI,CAAC,sBAAsB;qBACtD,CAAC,CAAC;oBACH,QAAQ,CAAC,IAAI,CAAC;wBACZ,QAAQ,EAAE,kCAAgB,CAAC,IAAI;wBAC/B,OAAO,EAAE,kCAAgB,CAAC,IAAI,CAAC,cAAc;qBAC9C,CAAC,CAAC;gBACL,CAAC;gBACD,MAAM;QACV,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,6BAA6B,CAAC,IAA2B;QACtE,MAAM,QAAQ,GAAuB,EAAE,CAAC;QAExC,gDAAgD;QAChD,MAAM,cAAc,GAAG,EAAE,CAAC;QAC1B,IAAI,IAAI,CAAC,kBAAkB,KAAK,+BAAa,CAAC,EAAE;YAAE,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACjF,IAAI,IAAI,CAAC,oBAAoB,KAAK,+BAAa,CAAC,EAAE;YAAE,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACrF,IAAI,IAAI,CAAC,iBAAiB,KAAK,+BAAa,CAAC,EAAE;YAAE,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE/E,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,MAAM,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;gBACzC,uCAAqB,CAAC,IAAI,CAAC,WAAiD,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAEvF,MAAM,iBAAiB,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,gBAAgB,CAAC,CAAC;YAErF,sCAAsC;YACtC,oBAAoB;YACpB,yCAAyC;YACzC,0DAA0D;YAC1D,QAAQ;YACR,IAAI;YAEJ,iCAAiC;YACjC,gEAAgE;YAChE,oBAAoB;YACpB,yCAAyC;YACzC,4DAA4D;YAC5D,QAAQ;YACR,IAAI;QACN,CAAC;QAED,+DAA+D;QAC/D,+BAA+B;QAC/B,oBAAoB;QACpB,yCAAyC;QACzC,+DAA+D;QAC/D,QAAQ;QACR,IAAI;QAEJ,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CACzB,YAAoB,EACpB,QAA4B,EAC5B,SAAkB;QAElB,OAAO,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC9B,YAAY;YACZ,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,SAAS,EAAE,SAAS,IAAI,QAAQ;SACjC,CAAC,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,YAAmB;QAC3C,IAAI,CAAC;YACH,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC;gBAAE,OAAO,EAAE,CAAC;YAEzC,MAAM,MAAM,GAAG,MAAM,sBAAM,CAAC,sBAAsB,CAAC,UAAU,CAAC;gBAC5D,IAAI,EAAE,YAAY;aACnB,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAClC,YAAoB,EACpB,WAAkC,EAClC,SAAkB;QAElB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAEpD,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxB,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAC7C,YAAY,EACZ,QAAQ,EACR,SAAS,CACV,CAAC;gBAEF,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YACzC,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,YAAY,EAAE,CAAC;YACtB,OAAO,CAAC,KAAK,CAAC,kDAAkD,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;YAC9F,8DAA8D;YAC9D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;CACF;AA5ND,4DA4NC"}